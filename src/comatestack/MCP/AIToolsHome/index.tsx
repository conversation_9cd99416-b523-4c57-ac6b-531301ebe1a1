import {Button} from '@panda-design/components';
import {MCPSquareLink, MCPPlaygroundLink} from '@/links/mcp';
import {IconArrowRight} from '@/icons/mcp';
import mcpAIToolsTitle from '@/assets/mcp/mcpAIToolsTitle.svg';
import {createLink} from '@/links/createLink';
import {features, producerSteps, consumerSteps} from './constants';
import {ProcessCardComponent} from './components';
import {
    Container,
    TopNavArea,
    MainTitleContainer,
    Description,
    ButtonArea,
    FeaturesContainer,
    FeatureCard,
    FeatureTitle,
    FeatureContent,
    IconWrapper,
    ProcessContainer,
    DetailButton,
} from './styles';

const ProducerDocLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/B8wSneaLSC/U9Q0FnOjJOfXaW#anchor-226db8e1-4c0c-11f0-a443-a55a2e881acb');
const ConsumerDocLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/B8wSneaLSC/U9Q0FnOjJOfXaW#anchor-16bd1400-4c20-11f0-a443-a55a2e881acb');
const UseDocLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/B8wSneaLSC/U9Q0FnOjJOfXaW');
const AIToolsHome = () => {

    return (
        <Container>
            <TopNavArea>
                <UseDocLink>使用文档<IconArrowRight /></UseDocLink>
            </TopNavArea>

            <MainTitleContainer>
                <img src={mcpAIToolsTitle} alt="MCP AI Tools Title" />
                <Description>
                    厂内MCP服务中心：一站式解决AI Agent工具生产与调用难题
                </Description>
                <ButtonArea>
                    <MCPSquareLink>
                        <Button type="primary">去MCP广场体验</Button>
                    </MCPSquareLink>
                    <MCPPlaygroundLink>
                        <Button>去Playground试用</Button>
                    </MCPPlaygroundLink>
                </ButtonArea>
            </MainTitleContainer>

            <FeaturesContainer>
                {features.map((feature, index) => {
                    const IconComponent = feature.icon;
                    return (
                        <FeatureCard key={index}>
                            <FeatureTitle>{feature.title}</FeatureTitle>
                            <FeatureContent>{feature.content}</FeatureContent>
                            <IconWrapper>
                                <IconComponent style={{fontSize: 56}} />
                            </IconWrapper>
                        </FeatureCard>
                    );
                })}
            </FeaturesContainer>

            <ProcessContainer>
                <ProcessCardComponent
                    title="工具生产者使用流程"
                    steps={producerSteps}
                    detailLink={
                        <ProducerDocLink>
                            <DetailButton type="text" size="small">
                                查看详情<IconArrowRight />
                            </DetailButton>
                        </ProducerDocLink>
                    }
                />
                <ProcessCardComponent
                    title="工具消费者使用流程"
                    steps={consumerSteps}
                    detailLink={
                        <ConsumerDocLink>
                            <DetailButton type="text" size="small">
                                查看详情<IconArrowRight />
                            </DetailButton>
                        </ConsumerDocLink>
                    }
                />
            </ProcessContainer>
        </Container>
    );
};

export default AIToolsHome;
